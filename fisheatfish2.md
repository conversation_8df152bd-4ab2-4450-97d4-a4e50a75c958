Product Requirements Document: Fish Eat Fish (React Native Version)
Version: 5.0
Date: 2025-05-25

1. Overview
1.1. Introduction
This document outlines the requirements for the enhanced version of "Fish Eat Fish," a 2D arcade-style game developed using React Native and a suitable game engine/library (e.g., React Native Game Engine, possibly with Matter.js for physics). Building upon the original concept, this version introduces multiplayer functionality, a persistent leaderboard, diverse enemy types with unique behaviors, a dynamic prey/weakness system, and visual player progression. The core gameplay remains centered around controlling a fish that consumes smaller entities to grow larger while avoiding larger predators in a persistent online environment.

1.2. Goals
Provide an engaging and competitive multiplayer "eat and grow" experience.

Implement a robust leaderboard system to foster competition and replayability.

Introduce diverse enemy types and mechanics (prey/weakness) for deeper gameplay.

Offer clear visual feedback for player growth, status, and interactions.

Ensure smooth performance and intuitive controls on target platforms (iOS, Android, potentially desktop via solutions like Electron if web technologies are used within RN).

Establish a foundation for potential future expansions (new modes, power-ups).

1.3. Target Audience
Casual gamers seeking competitive, quick-session online games.

Players who enjoy progression and leaderboard climbing.

Fans of arcade-style "eat and grow" games on mobile platforms.

2. Functional Requirements
2.1. Core Gameplay Loop
Control: The player controls a fish using an on-screen virtual analog stick for movement on mobile (implemented using React Native components and gesture handlers like react-native-gesture-handler), or the mouse cursor on desktop (if desktop support is pursued). The fish's direction and speed (within normal limits) are determined by the analog stick's position. Movement is managed by the chosen game engine's input handling and entity system.

Movement: Player movement incorporates inertia for a smoother feel. Movement is primarily horizontal with a slower overall pace for better gameplay experience.

Boost: Players can activate a temporary speed boost by applying a 'deeper tap' or extending the virtual analog stick to its maximum range on mobile, or by clicking/holding the left mouse button (desktop). This 'deeper tap' or maximum extension will trigger the boost. Boosting consumes the player's growth progress and can even revert the player to smaller size levels if used extensively. Visual effects indicate when boost is active.

Eating: Players grow by consuming:

Smaller Fish: Eating other fish that are strictly smaller (lower sizeLevel) than the player grants points and contributes to growth progress. A player cannot eat a fish of the same sizeLevel.

Food Particles: Small, passive food particles spawn in the world, providing minor growth progress when consumed.

Growth:

Consuming fish/food fills a growth progress bar displayed at the bottom of the screen (React Native UI component).

When the progress bar is full, the player advances to the next sizeLevel.

Growth increases the player's fish radius (managed by game engine entities/components), slightly decreases base speed, and increases the fishNeededToGrow for the next level.

Player's visual appearance (size, shape, and color, using game engine sprites/animations or React Native views if applicable) evolves as they reach new size levels.

Visual indicators show when fish can be eaten (green outline) or are dangerous (red outline).

Predation: Players are eliminated if they collide with a fish that is strictly larger (higher sizeLevel) than them. Collision with a fish of the same sizeLevel results in no eating or predation; the fish may pass through or bounce off each other depending on the chosen physics interaction model (e.g., managed by Matter.js within React Native Game Engine), but no damage or consumption occurs.

Scoring: Points are awarded for eating fish, with larger fish worth more points. Score contributes to leaderboard ranking.

Game Over: Occurs when the player's fish is eaten. A game over screen (React Native UI overlay) appears with a replay button rather than automatic respawning.

Fish Visibility: Fish within the player's vision area are highlighted with subtle colored outlines indicating if they're edible (green), dangerous (red), or similar in size (yellow).

2.2. Enemy Fish & AI (Leveraging the chosen Game Engine's System)
Spawning: Enemies spawn periodically from the edges of the large game world. Fish are scattered throughout the map rather than concentrated at the top. Enemy entities will be managed by the game engine.

Types: Enemy fish have a simplified design with consistent behavior:

Regular Fish: Standard behavior and points. Color varies by size.

Fish change size, shape, and color when they grow, matching the same growth mechanic as the player.

AI Behavior:

Movement: Enemies move across the world with primarily horizontal movement, bouncing off world boundaries. They exhibit semi-random movement patterns, occasionally changing direction. Implemented using game engine logic for entity movement.

No Water Flow: The game environment has no water current/flow that affects fish movement.

Visual Indicators:

Fish within the player's vision range have subtle colored outlines:

Red outline for fish that are dangerous to the player (strictly larger / higher sizeLevel)

Green outline for fish that can be eaten by the player (strictly smaller / lower sizeLevel)

Yellow outline for fish of similar size to the player (same sizeLevel - no eating/predation occurs on contact).

2.3. Multiplayer
Mode: The primary game mode is Player vs. Player (PvP) within a shared world instance with leaderboard functionality.

Server: Requires either a Node.js or Python backend server to manage game state, player synchronization, and leaderboard data. Communication with the React Native client will be via WebSockets or a similar protocol (e.g., gRPC).

Connection: The React Native client attempts to connect to the server upon starting a game. If connection fails, it defaults to single-player mode (no other players visible, leaderboard shows local player data if implemented).

Synchronization: Player positions, scores, sizes, names, and visual appearances are synchronized across all connected clients. Enemy states and food positions are managed and synchronized by the server.

Player Representation: Other players are visible in the game world as game engine entities with their current size and appearance.

No AI Players: The game focuses on real player interactions without AI-controlled fish.

2.4. Leaderboard
Functionality: An in-game leaderboard (React Native UI overlay) displays the names and current scores of all connected players, ranked in real-time.

Persistence: Scores are managed by the server and match the UI display.

UI: Displayed in the top-right corner with a semi-transparent background that matches the game's aesthetic, built using React Native components.

Visual Style: The leaderboard has a transparent background with subtle borders and highlights the current player's entry.

2.5. World & Environment (Managed by the Game Engine)
World Size: The game takes place in a large world, significantly bigger than the visible screen area (worldWidth, worldHeight), managed by the game engine's camera and world system.

Camera: The game engine's camera follows the player's fish, keeping it centered on the screen. There is no edge attraction effect at screen boundaries.

Minimap: A minimap (React Native UI overlay) in the top-left corner displays:

The player's position relative to the overall world boundaries

Only entities within the player's vision area

A transparent background that matches the game's aesthetic

Color-coded dots representing different entities (player, food, other fish)

An elliptical vision area that matches the player's screen view

Background: Features a clean underwater background (e.g., using parallax techniques within the game engine or layered React Native views) without distracting elements.

Full Screen Mode: The game should utilize the full available screen space, which is typical for React Native mobile apps.

2.6. Levels & Difficulty
Level Progression: Players advance through size levels by filling their growth progress bar.

Growth Mechanics:

Each level requires more food/fish to be eaten than the previous level

The progress bar (React Native UI component) at the bottom center shows progress toward the next size level

The label above the progress bar shows "Level" instead of "Size"

Visual Feedback:

Fish change size, shape, and color (game engine sprites/animations) when leveling up

A growth animation (game engine effect) plays when the player levels up

The progress bar updates in real-time, including when boost is being used

Boost Mechanics:

Boosting consumes growth progress

If all growth progress in the current level is consumed, the player will shrink to the previous level

Boosting can continue until the player returns to the smallest fish size

Visual effects (game engine particle effects or shaders if supported) indicate when boost is active

2.7. User Interface (UI) & User Experience (UX) (React Native Components for Overlays)
Main Menu:

"Start Game" button.

Player Name input field.

"How to Play" button/section (leading to a separate screen or modal).

In-Game HUD:

Growth Progress Bar at the bottom center with "Level" label.

Live Leaderboard in the top-right with transparent background.

Minimap in the top-left with transparent background.

No score display, player rank, player name, or connection status displays to keep the UI clean.

Game Over Screen:

"Game Over" message.

Final Score.

"Play Again" button.

"Main Menu" button.

Tutorial:

An interactive tutorial sequence for first-time players, explaining core mechanics (movement, eating, avoiding, boosting), potentially using the game engine's event system and React Native overlays for instructions.

Visual Feedback:

Clear visual distinction between fish sizes (game engine entity scaling).

Floating text for score gains (could be game engine text entities or React Native overlays).

Visual effects for eating, boosting, and level transitions (game engine effects).

Player fish appearance changes with growth/level.

Color-coded outlines for fish (green for edible, red for dangerous, yellow for similar size).

Sound & Music (Using React Native audio libraries e.g., react-native-sound, react-native-video for audio playback, or game engine's audio capabilities if available):

Background music loop.

Sound effects for eating, growth, boost, game over, etc.

2.8. Simplified Gameplay
No Special Fish Types:

The game uses a simplified approach without special fish types like golden, poisonous, or armored fish.

All fish follow the same basic mechanics, with size being the primary differentiator.

No Power-ups:

The game does not include power-ups to keep gameplay focused on the core eat-and-grow mechanics.

The boost feature provides sufficient strategic depth without additional power-ups.

No AI Players:

The game focuses on real player interactions in multiplayer mode.

In single-player mode, only regular enemy fish are present.

3. Non-Functional Requirements
3.1. Technology Stack
Client (Frontend): React Native framework (using JavaScript/TypeScript).

Game Engine/Library: e.g., React Native Game Engine, potentially integrated with a 2D physics library like Matter.js. Other options could include using a WebView to run a performant HTML5 game engine if native solutions prove too complex or limited for specific graphical needs, though direct native rendering is preferred.

UI Overlays: React Native components.

State Management (React Native): Redux, Zustand, React Context, or other suitable state management libraries.

Networking (React Native): fetch API, axios, or WebSocket libraries compatible with React Native (e.g., built-in WebSocket or socket.io-client).

Backend: Either Node.js or Python for the server implementation, with WebSockets (or gRPC) for multiplayer communication.

3.2. Performance
Target smooth 60 FPS gameplay on target mobile devices (iOS, Android midrange and above).

Optimize rendering, collision detection, and entity updates using the chosen game engine's capabilities and React Native/JavaScript best practices.

Minimize the impact of the React Native bridge on performance; opt for native modules or direct game engine rendering where possible for critical paths.

Minimize network latency impact on gameplay experience. Server logic should be efficient.

Monitor and optimize for mobile-specific constraints like battery consumption and thermal throttling.

3.3. Platform & Compatibility
Primary Target: Mobile platforms (iOS, Android).

Secondary Target (Optional): Desktop platforms (Windows, macOS, Linux) – this would likely require a different approach, possibly using Electron with the same core JavaScript game logic if a WebView-based game engine was chosen, or a separate desktop build if a truly native desktop game engine was used alongside React Native for UI. For this PRD, focus is on mobile.

UI/UX to be designed with a mobile-first approach.

3.4. Controls
Touch (Mobile): On-screen virtual analog stick for movement, implemented using React Native components and libraries like react-native-gesture-handler. Applying a 'deeper tap' or extending the virtual analog stick to its maximum range activates boost.

Mouse (Desktop - if pursued): Mouse movement for direction. Left-Click for boost.

Keyboard (Desktop - if pursued): Consider WASD or Arrow keys for movement as an alternative.

3.5. Scalability
The server should be designed to handle a moderate number of concurrent players per game instance (e.g., 10-20). Architecture should allow for potential future scaling (multiple game instances/servers).

Client-side (React Native/Game Engine) architecture should be scalable for adding new features, fish types, or game modes.

3.6. Code Quality
Maintain readable, well-commented, and modular code using JavaScript/TypeScript and React Native best practices.

Utilize the chosen game engine's component-based architecture or entity-component-system (ECS) principles.

Employ appropriate design patterns (e.g., State pattern for player/enemy states).

Implement comprehensive error handling and logging.

4. Success Metrics
Player Engagement: Average session length, frequency of play.

Retention: Daily/Weekly active users (DAU/WAU), first-time player tutorial completion rate.

Monetization (If applicable in future): Conversion rates, ARPPU.

Competitiveness: Leaderboard activity (score ranges, player rank distribution).

Performance:

Average client FPS on target devices.

App startup time.

Crash rate.

Server stability under load, low network latency.

User Feedback: Qualitative feedback gathered through app store reviews, user comments, or surveys.

5. Acceptance Criteria (Examples)
AC1 (Movement): Player fish follows the direction indicated by the on-screen virtual analog stick (mobile) or mouse input (desktop, if applicable) with smooth, primarily horizontal movement, leveraging the game engine's loop and entity updates.

AC2 (Eating): When the player fish (a game engine entity with collision detection) collides with an enemy fish entity that is strictly smaller (lower sizeLevel), the enemy fish is removed from the game, the player's score (managed in React Native state) increases, and the growth progress bar (React Native component) fills proportionally. No eating occurs if fish are the same size.

AC3 (Growth): Upon filling the growth progress bar, the player's sizeLevel increases, the fish's game engine entity visual appearance changes (size, sprite/animation), and the progress bar resets.

AC4 (Boost): Activating boost via 'deeper tap'/max extension of the analog stick (mobile) or left-click (desktop, if applicable) increases player speed (modifying game engine entity's velocity) and visibly drains the growth progress bar; ceasing the input stops the effect.

AC5 (Advanced Boost): When boost depletes the current level's growth progress, the player shrinks to the previous level (updating game engine entity and React Native state) and continues boosting with the new level's progress bar.

AC6 (Predation): When the player fish collides with an enemy fish entity that is strictly larger (higher sizeLevel), the game over screen (React Native UI) appears with a replay button. No predation occurs if fish are the same size.

AC7 (Visual Indicators): Game engine entities within the player's vision range display color-coded outlines (green for edible - strictly smaller, red for dangerous - strictly larger, yellow for similar size - same sizeLevel), rendered by the game engine.

AC8 (Minimap): The minimap (React Native UI overlay) shows an elliptical vision area that matches the player's screen view, with only entities within that area visible as color-coded dots.

AC9 (Transparent UI): The minimap and leaderboard (React Native components) have semi-transparent backgrounds that don't obstruct gameplay.

AC10 (Multiplayer): Other connected players are visible as game engine entities on screen, and their movements/size changes are reflected in near real-time based on server updates.

AC11 (Leaderboard): The in-game leaderboard (React Native UI) accurately displays the names and scores of connected players, sorted by score.

AC12 (Cursor Handling - Desktop, if applicable): The mouse cursor behaves as per platform standards, potentially fading or changing during active gameplay.

AC13 (Full Screen): The game runs full screen by default on mobile.