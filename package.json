{"name": "fisheatfish2", "version": "1.0.0", "description": "Fish Eat Fish - A multiplayer React Native game", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace FishEatFish2.xcworkspace -scheme FishEatFish2 -configuration Release -destination generic/platform=iOS -archivePath FishEatFish2.xcarchive archive"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.3", "@react-navigation/native": "^6.1.8", "@react-navigation/stack": "^6.3.18", "babel-plugin-module-resolver": "^5.0.2", "matter-js": "^0.19.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-game-engine": "^1.2.0", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.25.0", "react-native-sound": "^0.11.2", "react-native-svg": "^13.14.1", "react-native-vector-icons": "^10.0.2", "socket.io-client": "^4.7.2", "zustand": "^4.4.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/matter-js": "^0.19.6", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}}